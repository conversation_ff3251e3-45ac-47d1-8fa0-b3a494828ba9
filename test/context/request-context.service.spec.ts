import { RequestContext, RequestContextService } from '../../src/context/request-context.service';

describe('RequestContextService', () => {
  beforeEach(() => {
    // Clear any existing context before each test
    RequestContextService.clearValidationErrors();
  });

  describe('runWithContext', () => {
    it('should provide context within the callback', () => {
      const context: RequestContext = {
        requestId: 'test-123',
        isDebugMode: true,
        collectedLogs: []
      };

      RequestContextService.runWithContext(context, () => {
        expect(RequestContextService.getContext()).toEqual(context);
        expect(RequestContextService.isDebugMode()).toBe(true);
        expect(RequestContextService.getRequestId()).toBe('test-123');
      });
    });

    it('should not have context outside the callback', () => {
      const context: RequestContext = {
        requestId: 'test-123',
        isDebugMode: true,
        collectedLogs: []
      };

      RequestContextService.runWithContext(context, () => {
        // Context exists inside
        expect(RequestContextService.getContext()).toEqual(context);
      });

      // Context should not exist outside
      expect(RequestContextService.getContext()).toBeUndefined();
      expect(RequestContextService.isDebugMode()).toBe(false);
    });
  });

  describe('validation errors', () => {
    it('should collect validation errors in debug mode', () => {
      const context: RequestContext = {
        requestId: 'test-123',
        isDebugMode: true,
        collectedLogs: []
      };

      RequestContextService.runWithContext(context, () => {
        RequestContextService.addValidationErrors('id5');
        RequestContextService.addValidationErrors('gdpr');

        expect(RequestContextService.getValidationErrors()).toEqual(['id5', 'gdpr']);
      });
    });

    it('should not collect validation errors when not in debug mode', () => {
      const context: RequestContext = {
        requestId: 'test-123',
        isDebugMode: false,
        collectedLogs: []
      };

      RequestContextService.runWithContext(context, () => {
        RequestContextService.addValidationErrors('id5');

        expect(RequestContextService.getValidationErrors()).toEqual([]);
      });
    });

    it('should return empty array when no context exists', () => {
      expect(RequestContextService.getValidationErrors()).toEqual([]);
      expect(RequestContextService.isDebugMode()).toBe(false);
      expect(RequestContextService.getRequestId()).toBeUndefined();
    });
  });
});
