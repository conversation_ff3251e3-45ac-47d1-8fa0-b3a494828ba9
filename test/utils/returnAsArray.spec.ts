import { returnAsArray, returnAsArrayEmpty } from '../../src/utils/returnAsArray';

describe('returnAsArray test suite', () => {
  it('should convert element to array', () => {
    expect(returnAsArray('a')).toEqual(['a']);
    expect(returnAsArray(1)).toEqual([1]);
    expect(returnAsArray({})).toEqual([{}]);
  });

  it('should return array', () => {
    expect(returnAsArray([])).toEqual([]);
    expect(returnAsArray([1, 2, 3])).toEqual([1, 2, 3]);
  });
});

describe('returnAsArrayEmpty test suite', () => {
  it('should convert element to array', () => {
    expect(returnAsArray('a')).toEqual(['a']);
    expect(returnAsArray(1)).toEqual([1]);
    expect(returnAsArray({})).toEqual([{}]);
  });

  it('should return array', () => {
    expect(returnAsArray([])).toEqual([]);
    expect(returnAsArray([1, 2, 3])).toEqual([1, 2, 3]);
  });

  it('should convert null and undefined to empty array', () => {
    expect(returnAsArrayEmpty(null)).toEqual([]);
    expect(returnAsArrayEmpty(undefined)).toEqual([]);
  });
});
