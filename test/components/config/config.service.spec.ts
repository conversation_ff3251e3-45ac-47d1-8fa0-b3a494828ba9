import { Test, TestingModule } from '@nestjs/testing';
import { env } from '../../../src/env/envalidConfig';
import { ConfigService } from '../../../src/components/config/config.service';

describe('ConfigService', () => {
  let service: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConfigService]
    }).compile();

    service = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return the correct - hardcoded configuration', () => {
    const config = service.getConfig();

    expect(config).toEqual({
      aouserid: 'sprat_id',
      ppid: 'sprat_id',
      id5: 'id5',
      consent: 'gdpr',
      adserver_url: env.AD_SERVER_URL
    });
  });
});
