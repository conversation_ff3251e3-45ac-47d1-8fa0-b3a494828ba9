import { HttpException } from '@nestjs/common';
import { ValidationError } from 'joi';
import { handleJoiError } from '../../../src/common/joi-error-handler';

jest.mock('../../../src/logger/logger.service');

describe('handleJoiError', () => {
  it('returns 400 status code and message "Missing required parameter" if missing required param', () => {
    const error = {
      details: [{ type: 'any.required', message: '"foo" is required', path: ['foo'] }]
    } as any;

    const result = handleJoiError(error);
    expect(result).toBeInstanceOf(HttpException);
    expect(result.getStatus()).toBe(400);
    expect(result.getResponse()).toStrictEqual({
      message: 'Missing required parameter',
      details: [{ message: '"foo" is required', path: ['foo'] }]
    });
  });

  it('returns 422 status code and message "Invalid parameter value" if invalid param', () => {
    const error = {
      details: [{ type: 'string.base', message: '"foo" must be a string', path: ['foo'] }]
    } as any;

    const result = handleJoiError(error);
    expect(result.getStatus()).toBe(422);
    expect(result.getResponse()).toStrictEqual({
      message: 'Invalid parameter value',
      details: [{ message: '"foo" must be a string', path: ['foo'] }]
    });
  });

  it('returns 400 status code and message "Validation failed" if errors contain 422 and 400 errors', () => {
    const error = {
      details: [
        { type: 'any.required', message: '"foo" is required', path: ['foo'] },
        { type: 'string.base', message: '"bar" must be a string', path: ['bar'] }
      ]
    } as any;

    const result = handleJoiError(error);
    expect(result.getStatus()).toBe(400);
    expect(result.getResponse()).toStrictEqual({
      message: 'Validation failed',
      details: [
        { message: '"foo" is required', path: ['foo'] },
        { message: '"bar" must be a string', path: ['bar'] }
      ]
    });
  });

  it('returns 422 status code and message "Invalid parameter value" if error type is string.id5Prefix', () => {
    const error = {
      details: [
        { type: 'string.id5Prefix', message: '"foo" must have id5 prefix', path: ['foo'] }
      ]
    } as any;

    const result = handleJoiError(error);
    expect(result.getStatus()).toBe(422);
    expect(result.getResponse()).toStrictEqual({
      message: 'Invalid parameter value',
      details: [{ message: '"foo" must have id5 prefix', path: ['foo'] }]
    });
  });

  it('returns 422 and "Invalid parameter value" if error.details is undefined', () => {
    const error = {
      name: 'ValidationError',
      isJoi: true,
      details: undefined,
      annotate: () => '',
      _original: {}
    } as unknown as ValidationError;

    const result = handleJoiError(error);
    expect(result.getStatus()).toBe(422);
    expect(result.getResponse()).toStrictEqual({
      message: 'Invalid parameter value',
      details: []
    });
  });
});
