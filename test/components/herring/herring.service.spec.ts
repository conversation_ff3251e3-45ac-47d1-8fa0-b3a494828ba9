import { Test, TestingModule } from '@nestjs/testing';
import { ID5DecoderService } from '../../../src/components/ad/id5decoder.service';
import { HerringService } from '../../../src/components/herring/herring.service';
import { IdHelperService } from '../../../src/components/servicesWIthoutModule/idHelper.service';
import { LoggerService } from '../../../src/logger/logger.service';
import { RedisService } from '../../../src/redis/redis.service';
import { Id5ParamValue } from '../../../src/types';

describe('HerringService', () => {
  let service: HerringService;
  let redisService: RedisService;
  let id5DecoderService: ID5DecoderService;
  let idHelperService: IdHelperService;
  let loggerService: LoggerService;

  const mockRedisService = {
    get: jest.fn(),
    set: jest.fn()
  };

  const mockId5DecoderService = {
    decodeId5AndGetIdentity: jest.fn()
  };

  const mockIdHelperService = {
    generateNewRandomId: jest.fn(),
    getOrCreateHerringID: jest.fn()
  };

  const mockLoggerService = {
    setContext: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HerringService,
        {
          provide: RedisService,
          useValue: mockRedisService
        },
        {
          provide: ID5DecoderService,
          useValue: mockId5DecoderService
        },
        {
          provide: IdHelperService,
          useValue: mockIdHelperService
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ]
    }).compile();

    service = module.get<HerringService>(HerringService);
    redisService = module.get<RedisService>(RedisService);
    id5DecoderService = module.get<ID5DecoderService>(ID5DecoderService);
    idHelperService = module.get<IdHelperService>(IdHelperService);
    loggerService = module.get<LoggerService>(LoggerService);

    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('useragentHash', () => {
    it('should return correct MD5 hash substring for valid useragent', () => {
      const useragent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
      const hash = service['useragentHash'](useragent);

      expect(hash).toHaveLength(6);
      expect(typeof hash).toBe('string');
      expect(service['useragentHash'](useragent)).toBe(hash);
    });

    it('should handle undefined useragent', () => {
      const hash = service['useragentHash'](undefined);

      expect(hash).toHaveLength(6);
      expect(typeof hash).toBe('string');
    });

    it('should handle empty useragent', () => {
      const hash = service['useragentHash']('');

      expect(hash).toHaveLength(6);
      expect(typeof hash).toBe('string');
    });
  });

  describe('getProbCrossDeviceId', () => {
    it('should return undefined when wakeId is not provided', async () => {
      const result = await service['getProbCrossDeviceId'](undefined);

      expect(result).toBeUndefined();
      expect(mockRedisService.get).not.toHaveBeenCalled();
    });

    it('should return undefined when pcdId is not found in redis', async () => {
      const wakeId = 'test-wake-id';
      mockRedisService.get.mockResolvedValue(null);

      const result = await service['getProbCrossDeviceId'](wakeId);

      expect(result).toBeUndefined();
      expect(mockRedisService.get).toHaveBeenCalledWith(`pcd:${wakeId}`);
      expect(mockLoggerService.log).not.toHaveBeenCalled();
    });

    it('should return pcdId and log when found in redis', async () => {
      const wakeId = 'test-wake-id';
      const pcdId = 'test-pcd-id';
      mockRedisService.get.mockResolvedValue(pcdId);

      const result = await service['getProbCrossDeviceId'](wakeId);

      expect(result).toBe(pcdId);
      expect(mockRedisService.get).toHaveBeenCalledWith(`pcd:${wakeId}`);
      expect(mockLoggerService.log).toHaveBeenCalledWith('GET_PROB_CROSS_DEVICE_ID', {
        wakeId,
        pcdId
      });
    });
  });

  describe('isChrome', () => {
    it('should return true for Chrome useragent', () => {
      const ua =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
      const result = service['isChrome'](ua);

      expect(result).toBe(true);
    });

    it('should return false for Edge', () => {
      const ua =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59';
      const result = service['isChrome'](ua);

      expect(result).toBe(false);
    });

    it('should return false for Opera', () => {
      const ua =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277';
      const result = service['isChrome'](ua);

      expect(result).toBe(false);
    });

    it('should return false for Firefox', () => {
      const ua =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0';
      const result = service['isChrome'](ua);

      expect(result).toBe(false);
    });

    it('should return false for undefined useragent', () => {
      const result = service['isChrome'](undefined);

      expect(result).toBe(false);
    });
  });

  describe('getSpratId', () => {
    const baseArgs = {
      ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      taid: undefined,
      tpid: undefined,
      sid: undefined,
      id5Uid: undefined,
      boltProfileIdToken: undefined
    };

    beforeEach(() => {
      mockIdHelperService.getOrCreateHerringID.mockResolvedValue('test-sprat-id');
      mockIdHelperService.generateNewRandomId.mockReturnValue('random-sprat-id');
    });

    it('should use sid:cs key for Chrome with sid', async () => {
      const args = { ...baseArgs, sid: 'test-sid' };

      const result = await service['getSpratId'](args);

      expect(result).toBe('test-sprat-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        'sid:cs:test-sid',
        { isChrome: true, sid: 'test-sid' }
      );
      expect(mockLoggerService.log).toHaveBeenCalledWith('GET_SPRAT_ID', {
        herringCs: 'test-sprat-id'
      });
    });

    it('should use id5uid:cs key when id5Uid is provided', async () => {
      const args = { ...baseArgs, id5Uid: 'test-id5-uid' };

      const result = await service['getSpratId'](args);

      expect(result).toBe('test-sprat-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        'id5uid:cs:test-id5-uid',
        { id5Uid: 'test-id5-uid' }
      );
    });

    it('should use tpid:cs key when tpid is provided', async () => {
      const args = { ...baseArgs, tpid: 'test-tpid' };
      const uaHash = service['useragentHash'](args.ua);

      const result = await service['getSpratId'](args);

      expect(result).toBe('test-sprat-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        `tpid:cs:test-tpid:${uaHash}`,
        { tpid: 'test-tpid', uaHash }
      );
    });

    it('should use taid:cs key when taid is provided but tpid is not', async () => {
      const args = { ...baseArgs, taid: 'test-taid' };
      const uaHash = service['useragentHash'](args.ua);

      const result = await service['getSpratId'](args);

      expect(result).toBe('test-sprat-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        `taid:cs:test-taid:${uaHash}`,
        { taid: 'test-taid', uaHash }
      );
    });

    it('should use bpidt:cs key when boltProfileIdToken is provided', async () => {
      const args = { ...baseArgs, boltProfileIdToken: 'test-token' };
      const uaHash = service['useragentHash'](args.ua);

      const result = await service['getSpratId'](args);

      expect(result).toBe('test-sprat-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        `bpidt:cs:test-token:${uaHash}`,
        { boltProfileIdToken: 'test-token', uaHash }
      );
    });

    it('should generate random ID when no specific conditions are met', async () => {
      const args = { ...baseArgs };

      const result = await service['getSpratId'](args);

      expect(result).toBe('random-sprat-id');
      expect(mockIdHelperService.generateNewRandomId).toHaveBeenCalled();
      expect(mockIdHelperService.getOrCreateHerringID).not.toHaveBeenCalled();
    });
  });

  describe('getHerringIdWithSid', () => {
    it('should return herring ID from tpid;sid:cd key when found', async () => {
      const sid = 'test-sid';
      const expectedId = 'test-herring-id';
      mockRedisService.get.mockResolvedValueOnce(expectedId);

      const result = await service['getHerringIdWithSid'](sid);

      expect(result).toBe(expectedId);
      expect(mockRedisService.get).toHaveBeenCalledWith(`tpid;sid:cd:${sid}`);
      expect(mockRedisService.get).toHaveBeenCalledTimes(1);
      expect(mockLoggerService.log).toHaveBeenCalledTimes(1);
    });

    it('should return herring ID from taid;sid:cd key when tpid key returns null', async () => {
      const sid = 'test-sid';
      const expectedId = 'test-herring-id';
      mockRedisService.get.mockResolvedValueOnce(null).mockResolvedValueOnce(expectedId);

      const result = await service['getHerringIdWithSid'](sid);

      expect(result).toBe(expectedId);
      expect(mockRedisService.get).toHaveBeenCalledWith(`tpid;sid:cd:${sid}`);
      expect(mockRedisService.get).toHaveBeenCalledWith(`taid;sid:cd:${sid}`);
      expect(mockLoggerService.log).toHaveBeenCalledTimes(2);
    });

    it('should return undefined when both keys return null', async () => {
      const sid = 'test-sid';
      mockRedisService.get.mockResolvedValueOnce(null).mockResolvedValueOnce(null);

      const result = await service['getHerringIdWithSid'](sid);

      expect(result).toBeUndefined();
      expect(mockRedisService.get).toHaveBeenCalledTimes(2);
      expect(mockLoggerService.log).toHaveBeenCalledTimes(2);
    });
  });

  describe('getHerringId', () => {
    beforeEach(() => {
      mockIdHelperService.getOrCreateHerringID.mockResolvedValue('test-herring-id');
      mockIdHelperService.generateNewRandomId.mockReturnValue('random-herring-id');
    });

    it('should use tpid:cd key when tpid differs from taid', async () => {
      const args = {
        taid: 'test-taid',
        tpid: 'test-tpid',
        sid: 'test-sid',
        id5Uid: undefined,
        id5Individual: undefined,
        boltProfileIdToken: undefined,
        wakeId: undefined,
        pcdId: undefined
      };

      const result = await service['getHerringId'](args);

      expect(result).toBe('test-herring-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        'tpid:cd:test-tpid',
        { 'tpid:cd:test-tpid': 'test-tpid' }
      );
      expect(mockRedisService.set).toHaveBeenCalledWith(
        'taid;sid:cd:test-sid',
        'test-herring-id'
      );
      expect(mockRedisService.set).toHaveBeenCalledWith(
        'tpid;sid:cd:test-sid',
        'test-herring-id'
      );
    });

    it('should use taid:cd key when taid is provided and tpid is same or undefined', async () => {
      const args = {
        taid: 'test-taid',
        tpid: undefined,
        sid: undefined,
        id5Uid: undefined,
        id5Individual: undefined,
        boltProfileIdToken: undefined,
        wakeId: undefined,
        pcdId: undefined
      };

      const result = await service['getHerringId'](args);

      expect(result).toBe('test-herring-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        'taid:cd:test-taid',
        { 'taid:cd:test-taid': 'test-taid' }
      );
    });

    it('should handle sid-only case with existing herring ID', async () => {
      const args = {
        taid: undefined,
        tpid: undefined,
        sid: 'test-sid',
        id5Uid: undefined,
        id5Individual: undefined,
        boltProfileIdToken: undefined,
        wakeId: undefined,
        pcdId: undefined
      };

      jest
        .spyOn(service as any, 'getHerringIdWithSid')
        .mockResolvedValue('existing-herring-id');

      const result = await service['getHerringId'](args);

      expect(result).toBe('existing-herring-id');
      expect(service['getHerringIdWithSid']).toHaveBeenCalledWith('test-sid');
    });

    it('should use pcd_id key when pcdId is available in sid-only case', async () => {
      const args = {
        taid: undefined,
        tpid: undefined,
        sid: 'test-sid',
        id5Uid: undefined,
        id5Individual: undefined,
        boltProfileIdToken: undefined,
        wakeId: undefined,
        pcdId: 'test-pcd-id'
      };

      jest.spyOn(service as any, 'getHerringIdWithSid').mockResolvedValue(null);

      const result = await service['getHerringId'](args);

      expect(result).toBe('test-herring-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        'pcd_id:test-pcd-id',
        { pcdId: 'test-pcd-id' }
      );
      expect(mockLoggerService.log).toHaveBeenCalledWith('USING_PROBABILISTIC_GRAPH', {
        wakeId: undefined,
        pcdId: 'test-pcd-id',
        herringCd: 'test-herring-id'
      });
    });

    it('should use boltProfileIdToken when provided', async () => {
      const args = {
        taid: undefined,
        tpid: undefined,
        sid: undefined,
        id5Uid: undefined,
        id5Individual: undefined,
        boltProfileIdToken: 'test-token',
        wakeId: undefined,
        pcdId: undefined
      };

      const result = await service['getHerringId'](args);

      expect(result).toBe('test-herring-id');
      expect(mockIdHelperService.getOrCreateHerringID).toHaveBeenCalledWith(
        'bpidt:cd:test-token',
        { boltProfileIdToken: 'test-token' }
      );
    });

    it('should generate random ID as fallback', async () => {
      const args = {
        taid: undefined,
        tpid: undefined,
        sid: undefined,
        id5Uid: undefined,
        id5Individual: undefined,
        boltProfileIdToken: undefined,
        wakeId: undefined,
        pcdId: undefined
      };

      const result = await service['getHerringId'](args);

      expect(result).toBe('random-herring-id');
      expect(mockIdHelperService.generateNewRandomId).toHaveBeenCalled();
    });
  });

  describe('get', () => {
    const baseParams = {
      ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      taid: undefined,
      tpid: undefined,
      sid: 'test-sid',
      id5: 'ID5*test-id5' as Id5ParamValue,
      boltProfileIdToken: undefined,
      wakeId: 'test-wake-id'
    };

    beforeEach(() => {
      mockId5DecoderService.decodeId5AndGetIdentity.mockResolvedValue({
        id5Uid: 'test-id5-uid',
        identityId5: 'test-identity-id5'
      });
      mockRedisService.get.mockResolvedValue(null);
      mockIdHelperService.getOrCreateHerringID.mockResolvedValue('test-id');
      mockIdHelperService.generateNewRandomId.mockReturnValue('random-id');
    });

    it('should handle id5 consent "0"', async () => {
      const params = { ...baseParams, id5: '0' as Id5ParamValue };

      const result = await service.get(
        params.ua,
        params.taid,
        params.tpid,
        params.sid,
        params.id5,
        params.boltProfileIdToken,
        params.wakeId
      );

      expect(result).toEqual({
        csid: 'random-id',
        cdid: 'test-id'
      });
      expect(mockId5DecoderService.decodeId5AndGetIdentity).not.toHaveBeenCalled();
    });

    it('should process valid id5 and return IDs', async () => {
      const result = await service.get(
        baseParams.ua,
        baseParams.taid,
        baseParams.tpid,
        baseParams.sid,
        baseParams.id5,
        baseParams.boltProfileIdToken,
        baseParams.wakeId
      );

      expect(result).toEqual({
        csid: 'test-id',
        cdid: 'test-id'
      });

      expect(mockId5DecoderService.decodeId5AndGetIdentity).toHaveBeenCalledWith(
        baseParams.id5
      );
      expect(mockLoggerService.log).toHaveBeenCalledWith('ID_GENERATION_EVENT_START', {
        startTime: expect.any(Number)
      });
      expect(mockLoggerService.log).toHaveBeenCalledWith(
        'ID_GENERATION_EVENT_END',
        expect.objectContaining({
          spratId: 'test-id',
          herringId: 'test-id',
          timeTakenMs: expect.any(Number)
        })
      );
    });

    it('should handle all parameters correctly', async () => {
      const fullParams = {
        ua: baseParams.ua,
        taid: 'test-taid',
        tpid: 'test-tpid',
        sid: 'test-sid',
        id5: baseParams.id5,
        boltProfileIdToken: 'test-token',
        wakeId: 'test-wake-id'
      };

      const result = await service.get(
        fullParams.ua,
        fullParams.taid,
        fullParams.tpid,
        fullParams.sid,
        fullParams.id5,
        fullParams.boltProfileIdToken,
        fullParams.wakeId
      );

      expect(result).toEqual({
        csid: 'test-id',
        cdid: 'test-id'
      });
    });
  });

  describe('logIdGenerationEvent', () => {
    it('should log ID generation event with correct structure', () => {
      const args = {
        ua: 'test-ua',
        taid: 'test-taid',
        tpid: 'test-tpid',
        sid: 'test-sid',
        id5: 'ID5*test',
        boltProfileIdToken: 'test-token',
        wakeId: 'test-wake',
        pcdId: 'test-pcd',
        decodedIds: {
          id5Uid: 'test-uid',
          identityId5: 'test-identity'
        },
        startTime: 1234567890
      };

      service['logIdGenerationEvent'](args);

      expect(mockLoggerService.log).toHaveBeenCalledWith('ID_GENERATION_EVENT_START', {
        logEventObj: {
          createdAt: 1234567890,
          type: 'wake',
          modelInputs: {
            ua: 'test-ua',
            taid: 'test-taid',
            tpid: 'test-tpid',
            sid: 'test-sid',
            id5: 'ID5*test',
            boltProfileIdToken: 'test-token',
            wakeId: 'test-wake',
            pcdId: 'test-pcd'
          },
          probXdIds: {
            id5: {
              uid: 'test-uid',
              id5Individual: 'test-identity'
            },
            'pcd:baseline': {
              pcdId: 'test-pcd'
            }
          },
          timeTakenMs: 0
        }
      });
    });

    it('should handle null decodedIds and pcdId', () => {
      const args = {
        ua: 'test-ua',
        taid: undefined,
        tpid: undefined,
        sid: undefined,
        id5: undefined,
        boltProfileIdToken: undefined,
        wakeId: undefined,
        pcdId: undefined,
        decodedIds: null,
        startTime: 1234567890
      };

      service['logIdGenerationEvent'](args);

      expect(mockLoggerService.log).toHaveBeenCalledWith('ID_GENERATION_EVENT_START', {
        logEventObj: expect.objectContaining({
          probXdIds: {}
        })
      });
    });
  });
});
