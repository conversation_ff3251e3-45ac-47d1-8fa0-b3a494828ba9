import { UnprocessableEntityException } from '@nestjs/common';
import { HerringVersionAwarePipe } from '../../../src/components/herring/herring-version-aware.pipe';
import * as getSchemaForVersionFile from '../../../src/components/herring/herring.schema';
import { logger } from '../../../src/logger';
import { VersionEnum } from '../../../src/types';

jest.spyOn(console, 'trace').mockImplementation();

describe('HerringVersionAwarePipe', () => {
  let pipe: HerringVersionAwarePipe;
  let loggerSpy: jest.SpyInstance;
  let mockGetSchemaForVersion: jest.SpyInstance;
  let mockSchema: any;

  beforeEach(() => {
    loggerSpy = jest.spyOn(logger, 'error').mockImplementation();

    mockSchema = {
      validate: jest.fn()
    } as any;
    mockGetSchemaForVersion = jest
      .spyOn(getSchemaForVersionFile, 'getSchemaForVersion')
      .mockReturnValue(mockSchema);

    pipe = new HerringVersionAwarePipe();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  it('should be defined', () => {
    expect(pipe).toBeDefined();
  });

  describe('transform', () => {
    describe('version validation', () => {
      it('should set default version to v_1_0_0 when version is not provided', () => {
        const inputValue = { useragent: 'test' };
        const expectedOutput = { version: VersionEnum.v_1_0_0, useragent: 'test' };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: expectedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result.version).toBe(VersionEnum.v_1_0_0);
        expect(mockGetSchemaForVersion).toHaveBeenCalledWith(VersionEnum.v_1_0_0);
      });

      it('should accept valid version values', () => {
        const validVersions = [
          VersionEnum.v_1_0_0,
          VersionEnum.v_1_0_1,
          VersionEnum.v_1_1_0,
          VersionEnum.v_1_1_1,
          VersionEnum.v_1_1_2
        ];

        validVersions.forEach((version) => {
          const inputValue = { version, useragent: 'test' };
          const expectedOutput = { ...inputValue };

          mockSchema.validate.mockReturnValue({
            error: null,
            value: expectedOutput
          });

          const result = pipe.transform(inputValue);

          expect(result.version).toBe(version);
          expect(mockGetSchemaForVersion).toHaveBeenCalledWith(version);
        });
      });

      it('should throw BadRequestException for invalid version', () => {
        const inputValue = { version: 'invalid-version', useragent: 'test' };

        expect(() => pipe.transform(inputValue)).toThrow(UnprocessableEntityException);

        expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', expect.any(Object));
        expect(mockGetSchemaForVersion).not.toHaveBeenCalled();
      });

      it('should allow unknown fields during version validation', () => {
        const inputValue = {
          version: VersionEnum.v_1_0_1,
          useragent: 'test',
          unknownField: 'value',
          anotherField: 123
        };
        const expectedOutput = { ...inputValue };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: expectedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result).toEqual(expectedOutput);
      });
    });

    describe('data validation with version-specific schema', () => {
      it('should validate data using schema for the specified version', () => {
        const inputValue = {
          version: VersionEnum.v_1_1_0,
          useragent: 'test-agent',
          timestamp: 1234567890
        };
        const validatedOutput = {
          ...inputValue,
          additionalField: 'processed'
        };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: validatedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result).toEqual(validatedOutput);
        expect(mockGetSchemaForVersion).toHaveBeenCalledWith(VersionEnum.v_1_1_0);
        expect(mockSchema.validate).toHaveBeenCalledWith(inputValue, {
          abortEarly: false,
          convert: true,
          stripUnknown: false
        });
      });

      it('should return validated value when all validations pass', () => {
        const inputValue = {
          useragent: 'Mozilla/5.0',
          timestamp: 1234567890,
          ip: '127.0.0.1',
          domain: 'example.com',
          serviceId: 'test-service',
          cms: 'test-cms',
          wid: 'test-wid',
          sid: 'test-sid',
          id5: 'ID5*test-id5'
        };
        const expectedOutput = {
          version: VersionEnum.v_1_0_0,
          ...inputValue
        };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: expectedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result).toEqual(expectedOutput);
      });

      it('should throw BadRequestException when data validation fails', () => {
        const inputValue = { version: VersionEnum.v_1_0_0, invalidField: 'value' };
        const validationError = {
          details: [
            { message: 'useragent is required', path: ['useragent'] },
            { message: 'timestamp must be a number', path: ['timestamp'] }
          ]
        };

        mockSchema.validate.mockReturnValue({
          error: validationError,
          value: null
        });

        expect(() => pipe.transform(inputValue)).toThrow(UnprocessableEntityException);

        expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', {
          errDetails: validationError.details
        });
      });

      it('should handle multiple validation errors', () => {
        const inputValue = { version: VersionEnum.v_1_0_0 };
        const validationError = {
          details: [
            { message: 'useragent is required', path: ['useragent'] },
            { message: 'timestamp is required', path: ['timestamp'] },
            { message: 'ip is required', path: ['ip'] },
            { message: 'domain is required', path: ['domain'] }
          ]
        };

        mockSchema.validate.mockReturnValue({
          error: validationError,
          value: null
        });

        expect(() => pipe.transform(inputValue)).toThrow(UnprocessableEntityException);

        expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', {
          errDetails: validationError.details
        });
      });

      it('should handle validation errors with complex paths', () => {
        const inputValue = { version: VersionEnum.v_1_0_0, useragent: 'test' };
        const validationError = {
          details: [
            { message: 'nested field is invalid', path: ['parent', 'child', 'grandchild'] },
            { message: 'array index error', path: ['items', 0, 'value'] }
          ]
        };

        mockSchema.validate.mockReturnValue({
          error: validationError,
          value: null
        });

        expect(() => pipe.transform(inputValue)).toThrow(UnprocessableEntityException);

        expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', {
          errDetails: validationError.details
        });
      });
    });

    describe('error handling', () => {
      it('should format error details correctly', () => {
        const inputValue = { version: VersionEnum.v_1_0_0 };
        const joiError = {
          details: [
            {
              message: 'useragent is required',
              path: ['useragent']
            },
            {
              message: 'timestamp must be a number',
              path: ['timestamp', 'nested']
            }
          ]
        };

        mockSchema.validate.mockReturnValue({
          error: joiError,
          value: null
        });

        try {
          pipe.transform(inputValue);
          fail('Expected UnprocessableEntityException to be thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(UnprocessableEntityException);

          const temp = error as UnprocessableEntityException;
          expect(temp.message).toBe('Invalid parameter value');
          expect(temp.getResponse()).toEqual({
            message: 'Invalid parameter value',
            details: joiError.details
          });
        }
      });

      it('should handle empty error details', () => {
        const inputValue = { version: VersionEnum.v_1_0_0 };
        const joiError = {
          details: []
        };

        mockSchema.validate.mockReturnValue({
          error: joiError,
          value: null
        });

        expect(() => pipe.transform(inputValue)).toThrow(UnprocessableEntityException);
        expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', { errDetails: [] });
      });
    });

    describe('edge cases', () => {
      it('should handle empty object', () => {
        const inputValue = {};
        const expectedOutput = { version: VersionEnum.v_1_0_0 };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: expectedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result.version).toBe(VersionEnum.v_1_0_0);
      });

      it('should handle input with only version', () => {
        const inputValue = { version: VersionEnum.v_1_1_2 };
        const expectedOutput = { version: VersionEnum.v_1_1_2 };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: expectedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result.version).toBe(VersionEnum.v_1_1_2);
        expect(mockGetSchemaForVersion).toHaveBeenCalledWith(VersionEnum.v_1_1_2);
      });
    });
    describe('integration scenarios', () => {
      it('should handle complete validation flow for v1.0.0', () => {
        const inputValue = {
          useragent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          timestamp: 1234567890,
          ip: '***********',
          domain: 'example.com',
          serviceId: 'service123',
          cms: 'cms123',
          wid: 'wake123',
          sid: 'session123',
          id5: 'ID5*test123',
          extraField: 'should be preserved'
        };

        const validatedOutput = {
          version: VersionEnum.v_1_0_0,
          ...inputValue
        };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: validatedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result).toEqual(validatedOutput);
        expect(mockGetSchemaForVersion).toHaveBeenCalledWith(VersionEnum.v_1_0_0);
      });

      it('should handle version override scenario', () => {
        const inputValue = {
          version: VersionEnum.v_1_1_1,
          useragent: 'test',
          timestamp: 1234567890,
          accept_language: 'en-US'
        };

        const validatedOutput = {
          ...inputValue,
          processed: true
        };

        mockSchema.validate.mockReturnValue({
          error: null,
          value: validatedOutput
        });

        const result = pipe.transform(inputValue);

        expect(result.version).toBe(VersionEnum.v_1_1_1);
        expect(mockGetSchemaForVersion).toHaveBeenCalledWith(VersionEnum.v_1_1_1);
      });
    });
  });
});
