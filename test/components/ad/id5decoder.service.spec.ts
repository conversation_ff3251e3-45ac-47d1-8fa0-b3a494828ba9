import { Test, TestingModule } from '@nestjs/testing';
import { ID5DecoderService } from '../../../src/components/ad/id5decoder.service';
import { LoggerService } from '../../../src/logger/logger.service';
import { RedisService } from '../../../src/redis/redis.service';

jest.mock('../../../src/logger/logger.service');

describe('ID5DecoderService', () => {
  let service: ID5DecoderService;
  let redisService: RedisService;
  let loggerService: LoggerService;

  const mockRedisService = {
    get: jest.fn()
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ID5DecoderService,
        LoggerService,
        {
          provide: RedisService,
          useValue: mockRedisService
        }
      ]
    }).compile();

    service = module.get<ID5DecoderService>(ID5DecoderService);
    redisService = module.get<RedisService>(RedisService);
    loggerService = await module.resolve<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('decodeId5AndGetIdentity', () => {
    const validId5 = 'ID5*test123';

    it('should return null when decode fails', async () => {
      const decodeSpy = jest.spyOn(service, 'decode').mockResolvedValue(null);

      const result = await service.decodeId5AndGetIdentity(validId5);

      expect(result).toBeNull();
      expect(decodeSpy).toHaveBeenCalledWith(validId5);
      expect(loggerService.error).toHaveBeenCalledWith(
        'DECODE_ID5_AND_GET_IDENTITY_NO_ID5UID',
        { id5Uid: null }
      );
    });

    it('should return null when Redis returns no identity', async () => {
      const decodeSpy = jest.spyOn(service, 'decode').mockResolvedValue('decodedId5');
      mockRedisService.get.mockResolvedValue(null);

      const result = await service.decodeId5AndGetIdentity(validId5);

      expect(result).toBeNull();
      expect(decodeSpy).toHaveBeenCalledWith(validId5);
      expect(mockRedisService.get).toHaveBeenCalledWith('id5:decodedId5');
      expect(loggerService.warn).toHaveBeenCalledWith(
        'DECODE_ID5_AND_GET_IDENTITY_NO_IDENTITY',
        { id5Uid: 'decodedId5' }
      );
    });

    it('should return decoded identity when successful', async () => {
      const decodeSpy = jest.spyOn(service, 'decode').mockResolvedValue('decodedId5');
      mockRedisService.get.mockResolvedValue('identity123');

      const result = await service.decodeId5AndGetIdentity(validId5);

      expect(decodeSpy).toHaveBeenCalledWith(validId5);
      expect(mockRedisService.get).toHaveBeenCalledWith('id5:decodedId5');
      expect(result).toEqual({
        id5Uid: 'decodedId5',
        identityId5: 'identity123'
      });
      expect(loggerService.log).toHaveBeenCalledWith('DECODE_ID5_AND_GET_IDENTITY_END', {
        id5Uid: 'decodedId5',
        identity: 'identity123'
      });
    });
  });

  describe('decode', () => {
    it('should return null for invalid ID5 format (invalid base64)', async () => {
      const result = await service.decode('ID5*invalid');
      expect(result).toBeNull();
      expect(loggerService.error).toHaveBeenCalled();
    });

    it('should return null when key is not found', async () => {
      const getKeySpy = jest.spyOn(service as any, '_getKey').mockResolvedValue(null);

      const result = await service.decode('ID5*test');

      expect(result).toBeNull();
      expect(getKeySpy).toHaveBeenCalled();
      expect(loggerService.error).toHaveBeenCalledWith('DECODE_NO_KEY', {
        encryptedId5Id: 'ID5*test'
      });
    });

    it('should return null for unrecognized ID5', async () => {
      jest.spyOn(service as any, '_getKey').mockResolvedValue(new Int8Array([1, 2, 3]));
      jest.spyOn(service as any, '_getId5Id').mockReturnValue('ID5-UNRECOGNIZED-test');

      const result = await service.decode('ID5*test');

      expect(result).toBeNull();
      expect(loggerService.error).toHaveBeenCalledWith(
        'DECODE_ID5_UNRECOGNIZED',
        expect.any(String)
      );
    });

    it('should return decoded ID5 when successful', async () => {
      const mockKey = new Int8Array([1, 2, 3, 4]);
      jest.spyOn(service as any, '_getKey').mockResolvedValue(mockKey);
      jest.spyOn(service as any, '_getId5Id').mockReturnValue('ID5-TEST123');

      const result = await service.decode('ID5*test');

      expect(result).toBe('ID5-TEST123');
      expect(loggerService.log).toHaveBeenCalledWith('DECODE_END', 'ID5-TEST123');
    });

    it('should return null when no matching key found', async () => {
      const validKeys = {
        '123': { key1: [1, 2, 3], key2: [4, 5, 6] }
      };
      mockRedisService.get.mockResolvedValue(validKeys);

      const result = await service['_getKey'](
        new Int8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16])
      );

      expect(result).toBeNull();
      expect(loggerService.log).toHaveBeenCalledWith('NO_KEY_FOUND');
    });

    it('should return computed key when partner key found', async () => {
      const validKeys = {
        '258': {
          key1: new Int8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
          key2: new Int8Array([11, 12, 13, 14])
        }
      };
      mockRedisService.get.mockResolvedValue(validKeys);

      const partnersBlock = new Int8Array([
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16
      ]);
      const result = await service['_getKey'](partnersBlock);

      expect(result).toEqual(new Int8Array([9, 97, -96, -15]));
    });
  });

  describe('_getId5Id', () => {
    it('should create ID5 ID from bytes', () => {
      const uid = new Uint8Array([84, 69, 83, 84, 1, 2, 3, 4]);
      const result = service['_getId5Id'](uid);

      expect(result).toBe('ID5-TESTAQIDBA');
    });
  });

  describe('_splitPartnerBlock', () => {
    it('should split block into equal parts', () => {
      const block = new Int8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      const result = service['_splitPartnerBlock'](block, 4);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(new Int8Array([1, 2, 3, 4]));
      expect(result[1]).toEqual(new Int8Array([5, 6, 7, 8]));
    });

    it('should throw error for invalid block length', () => {
      const block = new Int8Array([1, 2, 3, 4, 5]);

      expect(() => {
        service['_splitPartnerBlock'](block, 4);
      }).toThrow('partners block length 5 is not divisible by 4');
    });
  });
});
