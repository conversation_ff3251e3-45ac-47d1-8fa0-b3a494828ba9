import * as Jo<PERSON> from 'joi';
import { JoiParamPipe } from '../../../src/components/ad/joi-param.pipe';
import { logger } from '../../../src/logger';

describe('JoiParamPipe', () => {
  let pipe: JoiParamPipe<any, any>;
  let mockSchema: any;

  let loggerSpy: jest.SpyInstance;

  beforeAll(() => {
    loggerSpy = jest.spyOn(logger, 'error').mockImplementation();

    mockSchema = {
      validate: jest.fn()
    };
  });

  describe('transform', () => {
    it('should return validated value when validation succeeds', () => {
      const inputValue = { test: 'value' };
      const validatedValue = { test: 'validated' };

      mockSchema.validate.mockReturnValue({
        error: undefined,
        value: validatedValue
      });

      pipe = new JoiParamPipe(mockSchema);
      const result = pipe.transform(inputValue);

      expect(result).toEqual(validatedValue);
      expect(mockSchema.validate).toHaveBeenCalledWith(inputValue, {
        abortEarly: false,
        convert: true,
        stripUnknown: false
      });
    });

    it('should handle null input values', () => {
      const validatedValue = { test: 'default' };

      mockSchema.validate.mockReturnValue({
        error: null,
        value: validatedValue
      });

      pipe = new JoiParamPipe(mockSchema);
      const result = pipe.transform(null);

      expect(result).toEqual(validatedValue);
      expect(mockSchema.validate).toHaveBeenCalledWith(null, {
        abortEarly: false,
        convert: true,
        stripUnknown: false
      });
    });

    it('should handle undefined input values', () => {
      const validatedValue = { test: 'default' };

      mockSchema.validate.mockReturnValue({
        error: null,
        value: validatedValue
      });

      pipe = new JoiParamPipe(mockSchema);
      const result = pipe.transform(undefined);

      expect(result).toEqual(validatedValue);
      expect(mockSchema.validate).toHaveBeenCalledWith(undefined, {
        abortEarly: false,
        convert: true,
        stripUnknown: false
      });
    });
  });

  describe('handleError', () => {
    it('should log formatted error details', () => {
      const joiError = {
        details: [
          { message: 'Field is required', path: ['field1'] },
          { message: 'Invalid format', path: ['field2', 'subfield'] }
        ]
      } as Joi.ValidationError;

      pipe = new JoiParamPipe(mockSchema);
      pipe['handleError'](joiError);

      expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', {
        errDetails: 'Field is required, Invalid format'
      });
    });

    it('should handle error with minimal details', () => {
      const joiError = {
        details: [{ message: 'Required', path: [] }]
      } as unknown as Joi.ValidationError;

      pipe = new JoiParamPipe(mockSchema);
      pipe['handleError'](joiError);

      expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', {
        errDetails: 'Required'
      });
    });
  });

  describe('Integration with real Joi schema', () => {
    it('should work with real Joi validation - success case', () => {
      const schema = Joi.object({
        name: Joi.string().required(),
        age: Joi.number().min(0).max(150)
      });

      const pipe = new JoiParamPipe(schema);
      const input = { name: 'John', age: 25 };

      const result = pipe.transform(input);
      expect(result).toEqual({ name: 'John', age: 25 });
    });

    it('should work with real Joi validation - error case', () => {
      const schema = Joi.object({
        name: Joi.string().required(),
        age: Joi.number().min(0).max(150).required()
      });

      const pipe = new JoiParamPipe(schema);
      const input = { name: '', age: -5 };

      pipe.transform(input);
      expect(loggerSpy).toHaveBeenCalledWith('VALIDATION', expect.any(Object));
    });

    it('should respect convert option', () => {
      const schema = Joi.object({
        count: Joi.number().required()
      });

      const pipe = new JoiParamPipe(schema);
      const input = { count: '42' };

      const result = pipe.transform(input);
      expect(result).toEqual({ count: 42 });
      expect(typeof result.count).toBe('number');
    });

    it('should respect stripUnknown: false option', () => {
      const schema = Joi.object({
        known: Joi.string().required()
      });

      const pipe = new JoiParamPipe(schema);
      const input = { known: 'test', unknown: 'should be kept' };

      const result = pipe.transform(input);
      expect(result).toEqual({ known: 'test', unknown: 'should be kept' });
    });
  });
});
