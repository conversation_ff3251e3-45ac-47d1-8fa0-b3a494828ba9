import { Test, TestingModule } from '@nestjs/testing';
import axios, { AxiosRequestConfig } from 'axios';
import { HttpService } from '../../src/http/http.service';
import { LoggerService } from '../../src/logger/logger.service';
import { xmlParser } from '../../src/utils';

describe('HttpService', () => {
  let service: HttpService;
  let loggerService: LoggerService;
  const mockLoggerService = {
    debug: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  };

  const mockXmlParser = jest.spyOn(xmlParser, 'fromXMLtoJSON');

  const toStringSpy = jest.fn();

  beforeEach(async () => {
    jest.spyOn(axios, 'create').mockReturnValue({
      get: (...args: any[]) => ({
        data: { toString: toStringSpy }
      })
    } as any);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HttpService,
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ]
    }).compile();

    service = module.get<HttpService>(HttpService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getJsonResponse', () => {
    const testUrl = 'https://api.example.com/data';
    const testConfig: AxiosRequestConfig = {
      headers: { 'User-Agent': 'TestAgent' },
      timeout: 5000
    };

    const mockXmlResponse = '<?xml version="1.0"?><root><item>test</item></root>';
    const mockParsedJson = { root: { item: 'test' } };

    beforeEach(() => {
      toStringSpy.mockReturnValue(mockXmlResponse);

      mockXmlParser.mockReturnValue(mockParsedJson);
    });

    it('should log HTTP request details', async () => {
      await service.getJsonResponse(testUrl, testConfig);

      expect(mockLoggerService.debug).toHaveBeenCalledWith('HTTP_REQUEST', {
        url: testUrl,
        config: testConfig
      });
    });

    it('should convert response data to string', async () => {
      toStringSpy.mockReturnValue(mockXmlResponse);

      await service.getJsonResponse(testUrl);

      expect(toStringSpy).toHaveBeenCalled();
    });

    describe('logging', () => {
      it('should log request details with config', async () => {
        const detailedConfig = {
          headers: {
            Authorization: 'Bearer token123',
            'Content-Type': 'application/json'
          },
          params: {
            page: 1,
            limit: 10
          },
          timeout: 10000
        };

        await service.getJsonResponse(testUrl, detailedConfig);

        expect(mockLoggerService.debug).toHaveBeenCalledWith('HTTP_REQUEST', {
          url: testUrl,
          config: detailedConfig
        });
      });

      it('should log request details without config', async () => {
        await service.getJsonResponse(testUrl);

        expect(mockLoggerService.debug).toHaveBeenCalledWith('HTTP_REQUEST', {
          url: testUrl,
          config: undefined
        });
      });

      it('should not log HTTP response (commented out in code)', async () => {
        await service.getJsonResponse(testUrl);

        expect(mockLoggerService.debug).not.toHaveBeenCalledWith(
          'HTTP_RESPONSE',
          expect.anything()
        );
      });
    });

    describe('generic type handling', () => {
      it('should work with different generic types', async () => {
        const mockApiResponse = {
          data: {
            users: [{ name: 'John', age: 30 }],
            total: 1
          }
        };
        const xmlParserResponse = {
          data: {
            users: {
              age: { _text: '30' },
              name: { _text: 'John' }
            },
            total: { _text: '1' }
          }
        };
        const mockedXmlResponse = xmlParser.fromJSONtoXML(mockApiResponse);
        mockXmlParser.mockRestore();
        toStringSpy.mockReturnValue(mockedXmlResponse);
        const result = await service.getJsonResponse<any>(testUrl);

        expect(result).toEqual(xmlParserResponse);
        expect(result.data.users).toStrictEqual({
          age: { _text: '30' },
          name: { _text: 'John' }
        });
        expect(result.data.total).toStrictEqual({ _text: '1' });
      });
    });
  });
});
